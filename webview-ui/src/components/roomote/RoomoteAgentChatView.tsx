import React, { useState, useEffect, useRef, useMemo, useCallback } from "react"
import { ArrowLeft } from "lucide-react"
import { Virtuoso, VirtuosoHandle } from "react-virtuoso"

import { <PERSON><PERSON> } from "@src/components/ui"
import { vscode } from "@src/utils/vscode"
import { combineApiRequests } from "@roo/combineApiRequests"
import { combineCommandSequences } from "@roo/combineCommandSequences"
import { getApiMetrics } from "@roo/getApiMetrics"
import type { ClineMessage, HistoryItem } from "@roo-code/types"
import ChatRow from "../chat/ChatRow"
import { useExtensionState } from "@src/context/ExtensionStateContext"
import TaskHeader from "../chat/TaskHeader"
// import { useSelectedModel } from "@src/components/ui/hooks/useSelectedModel"
import { getAllModes } from "@roo/modes"

// Remote Agent 聊天界面专用组件
export interface RemoteAgentChatViewProps {
	taskId: string
	roomoteApiUrl: string
	onBack?: () => void
	onClose?: () => void
	isReadOnly?: boolean
	historyItem?: HistoryItem
}

export const RemoteAgentChatView: React.FC<RemoteAgentChatViewProps> = ({
	taskId,
	onBack,
	onClose,
	isReadOnly = true, // eslint-disable-line @typescript-eslint/no-unused-vars
	historyItem,
}) => {
	const { customModes, mode, currentApiConfigName } = useExtensionState()
	const [messages, setMessages] = useState<ClineMessage[]>([])
	const [isConnected, setIsConnected] = useState(false)
	const [taskStatus, setTaskStatus] = useState<"pending" | "processing" | "completed" | "failed">("pending")
	const [connectionError, setConnectionError] = useState<string | null>(null)
	const [taskInfo, setTaskInfo] = useState<any>(null)
	const virtuosoRef = useRef<VirtuosoHandle>(null)
	const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({})
	const [showScrollToBottom, setShowScrollToBottom] = useState(false)
	const [isAtBottom, setIsAtBottom] = useState(false)

	// 获取实际的taskId（用于API调用）
	const actualTaskId = useMemo(() => {
		const realTaskId = historyItem?.remoteTaskId || taskId
		console.log(`[RemoteAgentChatView] actualTaskId calculation - historyItem.remoteTaskId: ${historyItem?.remoteTaskId}, taskId: ${taskId}, result: ${realTaskId}`)
		return realTaskId
	}, [historyItem?.remoteTaskId, taskId])

	// 从历史记录项或任务信息中获取配置，如果都没有则使用当前全局状态
	const taskConfig = useMemo(() => {
		if (historyItem?.remoteConfig) {
			return historyItem.remoteConfig
		}
		// 从后端返回的taskInfo.payload中解析配置信息
		if (taskInfo?.payload) {
			// 解析profileName - 如果有profileConfiguration，尝试从中解析，否则使用默认值
			let profileName = currentApiConfigName // 默认使用当前全局配置

			if (taskInfo.payload.profileConfiguration) {
				try {
					const profileConfig = JSON.parse(taskInfo.payload.profileConfiguration)
					// 从配置中提取profile名称
					profileName = profileConfig.profileName || profileConfig.name || currentApiConfigName
				} catch (error) {
					console.warn("Failed to parse profileConfiguration:", error)
				}
			}

			return {
				mode: taskInfo.payload.mode || mode,
				profileName: profileName,
			}
		}
		// 如果没有配置信息，使用当前全局状态作为默认值
		return {
			mode: mode, // 使用当前全局模式
			profileName: currentApiConfigName, // 使用当前全局配置名
		}
	}, [historyItem, taskInfo, mode, currentApiConfigName])

	// 获取模式的显示名称
	const modeDisplayName = useMemo(() => {
		if (!taskConfig.mode) return undefined
		const allModes = getAllModes(customModes)
		const mode = allModes.find((m) => m.slug === taskConfig.mode)
		return mode?.name || taskConfig.mode
	}, [taskConfig.mode, customModes])

	// 判断是否应该停止轮询
	const shouldStopPolling = useMemo(() => {
		const shouldStop = taskStatus === "completed" || taskStatus === "failed"
		console.log(`[RemoteAgentChatView] shouldStopPolling check - taskStatus: ${taskStatus}, shouldStop: ${shouldStop}`)
		return shouldStop
	}, [taskStatus])

	// 获取任务信息和状态
	useEffect(() => {
		console.log(`[RemoteAgentChatView] Initializing task ${actualTaskId}`)

		// 立即获取任务信息
		vscode.postMessage({
			type: "roomoteGetTaskInfo",
			taskId: actualTaskId,
		})

		// 立即获取任务消息
		vscode.postMessage({
			type: "roomoteGetTaskMessages",
			taskId: actualTaskId,
		})
	}, [actualTaskId])

	// 实时消息订阅
	useEffect(() => {
		if (shouldStopPolling) {
			console.log(`[RemoteAgentChatView] Stopping polling for task ${actualTaskId} - task completed/failed`)
			return
		}

		let isActive = true
		let pollingInterval: NodeJS.Timeout | null = null

		const fetchMessages = () => {
			if (!isActive) {
				console.log(`[RemoteAgentChatView] Fetch cancelled - component unmounted for task ${actualTaskId}`)
				return
			}

			if (shouldStopPolling) {
				console.log(`[RemoteAgentChatView] Fetch cancelled - task completed/failed for task ${actualTaskId}`)
				return
			}

			console.log(`[RemoteAgentChatView] Fetching messages for task ${actualTaskId}, status: ${taskStatus}`)
			vscode.postMessage({
				type: "roomoteGetTaskMessages",
				taskId: actualTaskId,
			})
		}

		// 立即获取一次消息
		fetchMessages()

		// 根据任务状态设置不同的轮询间隔
		const pollInterval = taskStatus === "processing" ? 2000 : 5000
		console.log(`[RemoteAgentChatView] Starting polling for task ${actualTaskId} with interval ${pollInterval}ms, status: ${taskStatus}`)

		pollingInterval = setInterval(fetchMessages, pollInterval)

		return () => {
			console.log(`[RemoteAgentChatView] Cleaning up polling for task ${actualTaskId}`)
			isActive = false
			if (pollingInterval) {
				clearInterval(pollingInterval)
			}
		}
	}, [actualTaskId, taskStatus, shouldStopPolling])

	// 定期刷新任务状态（只在未完成时）
	useEffect(() => {
		if (shouldStopPolling) {
			console.log(`[RemoteAgentChatView] Stopping status polling for task ${actualTaskId} - task completed/failed`)
			return
		}

		let isActive = true
		let statusInterval: NodeJS.Timeout | null = null

		const fetchTaskInfo = () => {
			if (!isActive) {
				console.log(`[RemoteAgentChatView] Status fetch cancelled - component unmounted for task ${actualTaskId}`)
				return
			}

			if (shouldStopPolling) {
				console.log(`[RemoteAgentChatView] Status fetch cancelled - task completed/failed for task ${actualTaskId}`)
				return
			}

			console.log(`[RemoteAgentChatView] Fetching task info for task ${actualTaskId}`)
			vscode.postMessage({
				type: "roomoteGetTaskInfo",
				taskId: actualTaskId,
			})
		}

		console.log(`[RemoteAgentChatView] Starting status polling for task ${actualTaskId} every 10s`)
		statusInterval = setInterval(fetchTaskInfo, 10000)

		return () => {
			console.log(`[RemoteAgentChatView] Cleaning up status polling for task ${actualTaskId}`)
			isActive = false
			if (statusInterval) {
				clearInterval(statusInterval)
			}
		}
	}, [actualTaskId, shouldStopPolling])

	// 监听来自 VS Code 扩展的消息响应
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data

			switch (message.type) {
				case "roomoteTaskInfo":
					if (message.taskId === actualTaskId) {
						console.log(`[RemoteAgentChatView] Received task info for ${actualTaskId}:`, message)
						if (message.success) {
							const newStatus = message.taskInfo.status || "pending"
							console.log(`[RemoteAgentChatView] Task ${actualTaskId} status updated: ${taskStatus} -> ${newStatus}`)
							setTaskInfo(message.taskInfo)
							setTaskStatus(newStatus)
							setConnectionError(null)
							setIsConnected(newStatus === "processing")
						} else {
							console.error(`[RemoteAgentChatView] Failed to get task info for ${actualTaskId}:`, message.error)
							setConnectionError(message.error || "无法获取任务信息")
							setIsConnected(false)
						}
					}
					break

				case "roomoteTaskMessages":
				case "remoteTaskMessages":
					if (message.taskId === actualTaskId) {
						console.log(`[RemoteAgentChatView] Received messages for ${actualTaskId}:`, message)
						if (message.success) {
							const newMessages = message.messages || []
							console.log(`[RemoteAgentChatView] Task ${actualTaskId} messages updated: ${messages.length} -> ${newMessages.length}`)
							setMessages(newMessages)
							setConnectionError(null)
						} else {
							console.error(`[RemoteAgentChatView] Failed to get messages for ${actualTaskId}:`, message.error)
							setIsConnected(false)
							setConnectionError(message.error || "无法获取任务消息")
						}
					}
					break
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [actualTaskId, taskStatus, messages.length])

	// 处理消息组合和过滤
	const processedMessages = useMemo(() => {
		if (!messages.length) {
			console.log(`[RemoteAgentChatView] No raw messages for task ${actualTaskId}`)
			return []
		}

		console.log(`[RemoteAgentChatView] Processing ${messages.length} raw messages for task ${actualTaskId}`)
		console.log(`[RemoteAgentChatView] Raw messages:`, messages.map(m => ({ type: m.type, say: m.say, text: m.text?.substring(0, 100) + '...' })))

		try {
			const processed = combineApiRequests(combineCommandSequences(messages))
			console.log(`[RemoteAgentChatView] Processed to ${processed.length} messages for task ${actualTaskId}`)
			return processed
		} catch (error) {
			console.error(`Error processing messages for task ${taskId}:`, error)
			return messages
		}
	}, [messages, taskId, actualTaskId])

	// 计算API指标
	const apiMetrics = useMemo(() => getApiMetrics(processedMessages), [processedMessages])

	// 过滤消息列表，排除第一条任务消息避免与TaskHeader重复
	const filteredMessages = useMemo(() => {
		if (processedMessages.length === 0) {
			console.log(`[RemoteAgentChatView] No processed messages for task ${actualTaskId}`)
			return []
		}

		console.log(`[RemoteAgentChatView] Processing ${processedMessages.length} messages for task ${actualTaskId}`)

		// 找到第一条用户任务消息
		const firstUserMessageIndex = processedMessages.findIndex(
			(m) =>
				(m.type === "say" && m.say === "text" && m.text && !m.text.startsWith("{")) ||
				(m.type === "ask" && m.text && !m.text.startsWith("{")),
		)

		console.log(`[RemoteAgentChatView] First user message index: ${firstUserMessageIndex}`)

		// 对于Remote Agent，我们显示所有消息，不过滤第一条任务消息
		// 因为用户需要看到完整的执行过程
		const result = processedMessages
		console.log(`[RemoteAgentChatView] Filtered messages count: ${result.length}`)
		return result
	}, [processedMessages, actualTaskId])

	// 创建task对象用于TaskHeader，显示真实的任务信息
	const mockTask = useMemo((): ClineMessage => {
		// 尝试从第一条用户消息获取任务内容
		const firstUserMessage = processedMessages.find(
			(m) =>
				(m.type === "say" && m.say === "text" && m.text && !m.text.startsWith("{")) ||
				(m.type === "ask" && m.text && !m.text.startsWith("{")),
		)

		// 如果找到了用户的原始任务内容，使用它；否则使用默认内容
		const taskText =
			firstUserMessage?.text ||
			(taskConfig.mode || taskConfig.profileName
				? `Remote Agent 任务 (${modeDisplayName || taskConfig.mode || "Unknown Mode"})`
				: `Remote Agent Task - ${taskId}`)

		return {
			type: "say",
			say: "text",
			text: taskText,
			ts: Date.now(),
			images: firstUserMessage?.images || [],
		}
	}, [processedMessages, taskId, taskConfig.mode, taskConfig.profileName, modeDisplayName])

	// TaskHeader需要的处理函数
	const handleCondenseContext = useCallback(() => {
		// Remote Agent不支持context condensing，可以留空或显示提示
		console.log("Context condensing not supported for Remote Agent tasks")
	}, [])

	const handleTaskClose = useCallback(() => {
		if (onClose) {
			onClose()
		}
	}, [onClose])

	// 自动滚动到底部
	useEffect(() => {
		if (filteredMessages.length > 0 && !historyItem) {
			setTimeout(() => {
				virtuosoRef.current?.scrollTo({ top: Number.MAX_SAFE_INTEGER, behavior: "smooth" })
			}, 100)
		}
	}, [filteredMessages.length, historyItem])

	// 处理行展开
	const handleToggleExpand = useCallback((ts: number) => {
		setExpandedRows((prev) => ({ ...prev, [ts]: !prev[ts] }))
	}, [])

	const handleRowHeightChange = useCallback(
		(isTaller: boolean) => {
			if (!isAtBottom && isTaller) {
				setTimeout(() => {
					virtuosoRef.current?.scrollTo({ top: Number.MAX_SAFE_INTEGER, behavior: "smooth" })
				}, 0)
			}
		},
		[isAtBottom],
	)

	// 消息渲染项
	const itemContent = useCallback(
		(index: number, message: ClineMessage) => {
			return (
				<ChatRow
					key={message.ts}
					message={message}
					isExpanded={expandedRows[message.ts] || false}
					onToggleExpand={handleToggleExpand}
					lastModifiedMessage={filteredMessages.at(-1)}
					isLast={index === filteredMessages.length - 1}
					onHeightChange={handleRowHeightChange}
					isStreaming={false} // Remote Agent 不需要流式显示
				/>
			)
		},
		[expandedRows, handleToggleExpand, filteredMessages, handleRowHeightChange],
	)

	// 获取状态显示信息
	const getStatusInfo = () => {
		// 优先使用最新获取的状态，只有在没有获取到时才使用历史状态
		const baseStatus = taskStatus || historyItem?.remoteStatus

		console.log(`[RemoteAgentChatView] Status info - taskStatus: ${taskStatus}, historyStatus: ${historyItem?.remoteStatus}, baseStatus: ${baseStatus}, isConnected: ${isConnected}`)

		switch (baseStatus) {
			case "completed":
				return {
					icon: <span className="w-3 h-3 rounded-full bg-green-400"></span>,
					text: "已完成",
					color: "text-green-400",
				}
			case "failed":
				return {
					icon: <span className="w-3 h-3 rounded-full bg-red-400"></span>,
					text: "已失败",
					color: "text-red-400",
				}
			case "processing":
				return isConnected
					? {
							icon: <span className="w-3 h-3 rounded-full bg-green-400"></span>,
							text: "执行中",
							color: "text-green-400",
						}
					: {
							icon: <span className="w-3 h-3 rounded-full bg-red-400"></span>,
							text: "连接中断",
							color: "text-red-400",
						}
			default:
				return {
					icon: <span className="w-3 h-3 rounded-full bg-yellow-400"></span>,
					text: "等待中",
					color: "text-yellow-400",
				}
		}
	}

	const statusInfo = getStatusInfo()

	return (
		<div className="fixed top-0 left-0 right-0 bottom-0 flex flex-col overflow-hidden">
			{/* 顶部标题栏 - 类似ChatView的TaskHeader位置 */}
			<div className="flex justify-between items-center px-4 py-2 bg-vscode-sideBar-background border-b border-vscode-input-border">
				<div className="flex items-center gap-2 min-w-0 flex-1">
					{onBack && (
						<Button onClick={onBack} className="p-1 shrink-0">
							<ArrowLeft className="w-4 h-4" />
						</Button>
					)}
					<h3 className="text-vscode-foreground m-0 truncate">Remote Agent</h3>
					<div className="flex items-center gap-1 text-xs shrink-0">
						{statusInfo.icon}
						<span className={statusInfo.color}>{statusInfo.text}</span>
					</div>
				</div>
				<div className="flex items-center gap-2 shrink-0">
					{onClose && (
						<Button onClick={onClose} className="p-1">
							完成
						</Button>
					)}
				</div>
			</div>

			{/* TaskHeader组件 */}
			<TaskHeader
				task={mockTask}
				tokensIn={apiMetrics.totalTokensIn}
				tokensOut={apiMetrics.totalTokensOut}
				cacheWrites={apiMetrics.totalCacheWrites}
				cacheReads={apiMetrics.totalCacheReads}
				totalCost={apiMetrics.totalCost}
				contextTokens={apiMetrics.contextTokens}
				buttonsDisabled={false}
				handleCondenseContext={handleCondenseContext}
				onClose={handleTaskClose}
				jobId={taskId}
				remoteTaskId={taskInfo?.taskId || historyItem?.remoteTaskId}
			/>

			{/* 连接错误显示 */}
			{connectionError && (
				<div className="px-4 py-2 bg-red-900/20 border-b border-red-800 text-red-400 text-sm">
					{connectionError}
				</div>
			)}

			{/* 消息列表 */}
			<div className="grow flex">
				{filteredMessages.length > 0 ? (
					<Virtuoso
						ref={virtuosoRef}
						data={filteredMessages}
						itemContent={itemContent}
						followOutput="smooth"
						increaseViewportBy={{ top: 1000, bottom: 1000 }}
						className="scrollable grow overflow-y-scroll mb-[5px]"
						style={{ height: "100%", width: "100%" }}
						atBottomStateChange={(isAtBottom) => {
							setIsAtBottom(isAtBottom)
							setShowScrollToBottom(!isAtBottom && !historyItem)
						}}
						atBottomThreshold={10}
						initialTopMostItemIndex={filteredMessages.length - 1}
					/>
				) : (
					<div className="flex flex-col items-center justify-center h-64 text-vscode-descriptionForeground space-y-4 w-full">
						{taskStatus === "completed" ? (
							<div className="text-center">
								<div className="text-lg">✅ 任务已完成</div>
								<div className="text-sm mt-2">
									{taskInfo?.completedAt && (
										<>完成时间: {new Date(taskInfo.completedAt).toLocaleString()}</>
									)}
								</div>
							</div>
						) : taskStatus === "failed" ? (
							<div className="text-center">
								<div className="text-lg">❌ 任务执行失败</div>
								<div className="text-sm mt-2">
									{taskInfo?.completedAt && (
										<>失败时间: {new Date(taskInfo.completedAt).toLocaleString()}</>
									)}
								</div>
							</div>
						) : taskStatus === "processing" ? (
							<div className="text-center">
								<div className="animate-pulse">🔄 任务执行中...</div>
								<div className="text-sm mt-2">{isConnected ? "等待消息..." : "正在连接..."}</div>
							</div>
						) : (
							<div className="text-center">
								<div>⏳ 任务等待中</div>
								<div className="text-sm mt-2">任务尚未开始执行</div>
							</div>
						)}
					</div>
				)}
			</div>

			{/* 滚动到底部按钮 */}
			{showScrollToBottom && (
				<div className="flex px-[15px] pt-[10px]">
					<div
						className="bg-[color-mix(in_srgb,_var(--vscode-toolbar-hoverBackground)_55%,_transparent)] rounded-[3px] overflow-hidden cursor-pointer flex justify-center items-center flex-1 h-[25px] hover:bg-[color-mix(in_srgb,_var(--vscode-toolbar-hoverBackground)_90%,_transparent)] active:bg-[color-mix(in_srgb,_var(--vscode-toolbar-hoverBackground)_70%,_transparent)]"
						onClick={() => {
							virtuosoRef.current?.scrollTo({ top: Number.MAX_SAFE_INTEGER, behavior: "smooth" })
						}}
						title="滚动到底部">
						<span className="codicon codicon-chevron-down text-[18px]"></span>
					</div>
				</div>
			)}

			{/* 任务配置信息 - 始终显示，不管是否为只读模式 */}
			{(taskConfig.mode || taskConfig.profileName || modeDisplayName) && (
				<div className="px-4 py-2 bg-vscode-sideBar-background border-t border-vscode-input-border">
					<div className="flex items-center gap-1 min-w-0">
						{/* 模式选择器 */}
						{modeDisplayName && (
							<div className="shrink-0">
								<div className="px-2 py-1 text-xs bg-vscode-input-background border border-vscode-input-border rounded text-vscode-input-foreground cursor-not-allowed relative whitespace-nowrap">
									{modeDisplayName}
									<span className="absolute right-1 top-1/2 transform -translate-y-1/2 pointer-events-none">
										<span className="codicon codicon-chevron-down text-vscode-descriptionForeground opacity-50 text-[10px]"></span>
									</span>
								</div>
							</div>
						)}

						{/* 配置选择器 */}
						{taskConfig.profileName && (
							<div className="shrink-0 max-w-[200px]">
								<div className="px-2 py-1 text-xs bg-vscode-input-background border border-vscode-input-border rounded text-vscode-input-foreground cursor-not-allowed relative overflow-hidden text-ellipsis whitespace-nowrap">
									{taskConfig.profileName}
									<span className="absolute right-1 top-1/2 transform -translate-y-1/2 pointer-events-none">
										<span className="codicon codicon-chevron-down text-vscode-descriptionForeground opacity-50 text-[10px]"></span>
									</span>
								</div>
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	)
}
